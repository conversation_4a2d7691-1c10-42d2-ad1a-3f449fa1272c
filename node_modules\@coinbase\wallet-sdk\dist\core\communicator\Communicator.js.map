{"version": 3, "file": "Communicator.js", "sourceRoot": "", "sources": ["../../../src/core/communicator/Communicator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAG5C,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAQrD;;;;;;;;GAQG;AACH,MAAM,OAAO,YAAY;IAOvB,YAAY,EAAE,GAAG,GAAG,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAuB;QAHpE,UAAK,GAAkB,IAAI,CAAC;QAC5B,cAAS,GAAG,IAAI,GAAG,EAA6D,CAAC;QAQzF;;WAEG;QACH,gBAAW,GAAG,KAAK,EAAE,OAAgB,EAAE,EAAE;YACvC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9C,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC,CAAC;QAEF;;WAEG;QACH,kCAA6B,GAAG,KAAK,EACnC,OAAoC,EACxB,EAAE;YACd,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAI,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC;YACvF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC1B,OAAO,MAAM,eAAe,CAAC;QAC/B,CAAC,CAAC;QAEF;;WAEG;QACH,cAAS,GAAG,KAAK,EAAqB,SAAqC,EAAc,EAAE;YACzF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,MAAM,QAAQ,GAAG,CAAC,KAAsB,EAAE,EAAE;oBAC1C,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM;wBAAE,OAAO,CAAC,oBAAoB;oBAElE,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;oBAC3B,IAAI,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;wBACvB,OAAO,CAAC,OAAO,CAAC,CAAC;wBACjB,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;wBAChD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC,CAAC;gBAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF;;WAEG;QACK,eAAU,GAAG,GAAG,EAAE;YACxB,+DAA+D;YAC/D,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAElB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,EAAE;gBAC9C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,CAAC;gBACxE,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC;QAEF;;WAEG;QACH,uBAAkB,GAAG,KAAK,IAAqB,EAAE;YAC/C,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACrC,yEAAyE;gBACzE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC;YACpB,CAAC;YAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEjC,IAAI,CAAC,SAAS,CAAgB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,aAAa,CAAC;iBAClE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;iBACrB,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;YAEnB,OAAO,IAAI,CAAC,SAAS,CAAgB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,aAAa,CAAC;iBACzE,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBAChB,IAAI,CAAC,WAAW,CAAC;oBACf,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,IAAI,EAAE;wBACJ,OAAO,EAAE,OAAO;wBAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;wBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;qBACrC;iBACF,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,KAAK;oBAAE,MAAM,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO,IAAI,CAAC,KAAK,CAAC;YACpB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QA5FA,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CA0FF"}