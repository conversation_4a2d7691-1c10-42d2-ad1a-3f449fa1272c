{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/sign/util.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AAUpE,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,MAAM,eAAe,GAAG,YAAY,CAAC;AACrC,MAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;AAEvE,MAAM,UAAU,cAAc;IAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAe,CAAC;AACxD,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,UAAsB;IACpD,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,eAAe,CAAC,MAMrC;IACC,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;IACtE,iCAAiC,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAEpF,MAAM,OAAO,GAAsC;QACjD,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;QACvB,KAAK,EAAE,kBAAkB;QACzB,IAAI,kCACC,MAAM,CAAC,UAAU,KACpB,gBAAgB,GACjB;KACF,CAAC;IACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;IAC3E,OAAO,IAAkB,CAAC;AAC5B,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,MAK5B;IACC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;IAChE,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,KAAK,CAAC,CAAC,CAAC;YACX,OAAO,IAAI,SAAS,CAAC;gBACnB,QAAQ;gBACR,QAAQ;gBACR,YAAY;aACb,CAAC,CAAC;QACL,CAAC;QACD,KAAK,YAAY,CAAC,CAAC,CAAC;YAClB,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,QAAQ;gBACR,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,iCAAiC,CAC9C,YAA0B,EAC1B,QAAqB,EACrB,QAA+B;IAE/B,MAAM,YAAY,CAAC,SAAS,CAAgB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,0BAA0B,CAAC,CAAC;IAEjG,0EAA0E;IAC1E,2DAA2D;IAC3D,MAAM,UAAU,GAAG,IAAI,gBAAgB,CAAC;QACtC,QAAQ;QACR,QAAQ;KACT,CAAC,CAAC;IAEH,oCAAoC;IACpC,YAAY,CAAC,WAAW,CAAC;QACvB,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,EAAE,OAAO,EAAE,UAAU,CAAC,UAAU,EAAE,EAAE;KAC1B,CAAC,CAAC;IAEpB,iCAAiC;IACjC,MAAM,UAAU,CAAC,SAAS,EAAE,CAAC;IAE7B,iCAAiC;IACjC,YAAY,CAAC,WAAW,CAAC;QACvB,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;KACT,CAAC,CAAC;AACtB,CAAC"}