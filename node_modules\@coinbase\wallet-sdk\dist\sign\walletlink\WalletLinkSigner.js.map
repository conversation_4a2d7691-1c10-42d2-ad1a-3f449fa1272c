{"version": 3, "file": "WalletLinkSigner.js", "sourceRoot": "", "sources": ["../../../src/sign/walletlink/WalletLinkSigner.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,6DAA6D;AAC7D,cAAc;AACd,OAAO,MAAM,MAAM,2CAA2C,CAAC;AAE/D,OAAO,EAAE,2BAA2B,EAAE,MAAM,sBAAsB,CAAC;AAEnE,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAEvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,OAAO,EACL,iBAAiB,EACjB,mBAAmB,EACnB,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,sBAAsB,EACtB,mBAAmB,EACnB,mBAAmB,GACpB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,MAAM,oBAAoB,GAAG,gBAAgB,CAAC;AAC9C,MAAM,oBAAoB,GAAG,mBAAmB,CAAC;AAIjD,0IAA0I;AAC1I,MAAM,OAAO,gBAAgB;IAO3B,YAAY,OAAoE;QALxE,WAAM,GAA2B,IAAI,CAAC;QAEtC,eAAU,GAAoB,EAAE,CAAC;QAIvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;QAEzC,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC3E,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAoB,CAAC;YAChE,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;gBACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,UAAU;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;QACpD,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACpC,CAAC;IAED,IAAY,eAAe;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;IACzC,CAAC;IAED,IAAY,UAAU;;QACpB,OAAO,MAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,CAAC,mCAAI,SAAS,CAAC;IAClE,CAAC;IAED,IAAY,UAAU,CAAC,KAAa;QAClC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAEO,kBAAkB,CAAC,UAAkB,EAAE,OAAe;;QAC5D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,uCAAuC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,eAAe,CAAC;QAClE,IAAI,YAAY,EAAE,CAAC;YACjB,MAAA,IAAI,CAAC,QAAQ,qDAAG,cAAc,EAAE,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,MAAoB;QAC3C,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAQ1D,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,MAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,kBAAkB,OAAO,CAAC,IAAI,oBAAoB,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAA,EAAE,CAAC;YACtB,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,OAAO,CAAA,EAAE,CAAC;YAC9B,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;QAE7D,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,UAAU,CACnC,OAAO,CAAC,IAAI,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,KAAK,EACL,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE,CACpB,CAAC;QAEF,IAAI,eAAe,CAAC,MAAM,CAAC;YAAE,OAAO,KAAK,CAAC;QAE1C,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAoB;;QACjD,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAWvB,CAAC;QAEF,IAAI,CAAA,MAAA,OAAO,CAAC,OAAO,0CAAE,MAAM,MAAK,CAAC,EAAE,CAAC;YAClC,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC1D,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC5B,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAE3D,IAAI,aAAa,KAAK,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAErC,MAAM,EACJ,OAAO,GAAG,EAAE,EACZ,iBAAiB,GAAG,EAAE,EACtB,SAAS,EACT,QAAQ,GAAG,EAAE,EACb,cAAc,GACf,GAAG,OAAO,CAAC;QAEZ,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,gBAAgB,CACtC,aAAa,CAAC,QAAQ,EAAE,EACxB,OAAO,EACP,QAAQ,EACR,iBAAiB,EACjB,SAAS,EACT,cAAc,CACf,CAAC;QAEF,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,OAAO,KAAK,CAAC;QAEvC,IAAI,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,UAAU,MAAK,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAoB;QACpD,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAEvB,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAErD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,mBAAmB,CACzC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,EACpB,IAAI,CAAC,eAAe,IAAI,SAAS,CAClC,CAAC;QAEF,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QAEpC,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC;QAClC,IAAI,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEO,aAAa,CAAC,SAAmB,EAAE,CAAW;;QACpD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9E,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;QAC/B,MAAA,IAAI,CAAC,QAAQ,qDAAG,iBAAiB,EAAE,YAAY,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAyB;QACrC,MAAM,MAAM,GAAI,OAAO,CAAC,MAAuB,IAAI,EAAE,CAAC;QAEtD,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,cAAc;gBACjB,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9B,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC;YACtC,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxC,KAAK,aAAa;gBAChB,OAAO,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YAEhD,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAErC,KAAK,eAAe,CAAC;YACrB,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAEjC,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAEpC,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAE3C,KAAK,wBAAwB;gBAC3B,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAE9C,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAE3C,KAAK,sBAAsB,CAAC;YAC5B,KAAK,sBAAsB,CAAC;YAC5B,KAAK,sBAAsB,CAAC;YAC5B,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAErC,KAAK,yBAAyB;gBAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEvC,KAAK,4BAA4B;gBAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE1C,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAEjC;gBACE,IAAI,CAAC,IAAI,CAAC,UAAU;oBAAE,MAAM,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;gBACpF,OAAO,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,aAAqB;QAC/C,MAAM,UAAU,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QACtD,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1F,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,EAWjC;QACC,MAAM,WAAW,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QAClF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAEtC,MAAM,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5D,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAClE,MAAM,aAAa,GAAG,EAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7E,MAAM,YAAY,GAAG,EAAE,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACpF,MAAM,oBAAoB,GACxB,EAAE,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACjF,MAAM,QAAQ,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,MAAM,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QAE7E,OAAO;YACL,WAAW;YACX,SAAS;YACT,QAAQ;YACR,IAAI;YACJ,KAAK;YACL,aAAa;YACb,YAAY;YACZ,oBAAoB;YACpB,QAAQ;YACR,OAAO;SACR,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,OAAyB;QAC/C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAErE,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC;YAClC,MAAM,EAAE,kCAAkC;YAC1C,MAAM,EAAE;gBACN,OAAO,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrC,SAAS,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,SAAS,EAAE,MAAM,KAAK,oBAAoB;aAC3C;SACF,CAAC,CAAC;QACH,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAEO,UAAU;;QAChB,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,CAAC,mCAAI,GAAG,EAAE,EAAE,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,oBAAoB;;QAChC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAA,IAAI,CAAC,QAAQ,qDAAG,SAAS,EAAE,EAAE,OAAO,EAAE,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;YAChF,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,uBAAuB,EAAE,CAAC;QAClD,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QAEpC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/B,MAAA,IAAI,CAAC,QAAQ,qDAAG,SAAS,EAAE,EAAE,OAAO,EAAE,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;QAChF,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,EAAE,MAAM,EAAoB;QACrD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAErE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAElC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC;YAClC,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE;gBACN,OAAO,EAAE,mBAAmB,CAAC,OAAO,CAAC;gBACrC,OAAO,EAAE,iBAAiB,CAAC,OAAO,CAAC;gBACnC,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,IAAI;aACpB;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAoB;QACrD,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAE3D,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAAoB;QACxD,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACxF,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAoB;QACrD,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAE3D,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,gCAAgC,CAAC,EAAE,CAAC,CAAC;QAC7D,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAyB;QACnD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAErE,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;YAC/B,MAAM,WAAW,GAAG;gBAClB,oBAAoB,EAAE,MAAM,CAAC,0BAA0B;gBACvD,oBAAoB,EAAE,MAAM,CAAC,uBAAuB;gBACpD,oBAAoB,EAAE,MAAM,CAAC,uBAAuB;gBACpD,iBAAiB,EAAE,MAAM,CAAC,uBAAuB;aAClD,CAAC;YACF,OAAO,mBAAmB,CACxB,WAAW,CAAC,MAAkC,CAAC,CAAC;gBAC9C,IAAI,EAAE,sBAAsB,CAAC,KAAK,CAAC;aACpC,CAAW,EACZ,IAAI,CACL,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,KAAK,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,KAAK,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAElC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC;YAClC,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE;gBACN,OAAO,EAAE,mBAAmB,CAAC,OAAO,CAAC;gBACrC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;gBACxB,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC/C,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,GAAG,CAAC;YAAE,MAAM,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC;gBAChC,UAAU,EAAE,cAAc;gBAC1B,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC/C,aAAa,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;aAClD,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF"}