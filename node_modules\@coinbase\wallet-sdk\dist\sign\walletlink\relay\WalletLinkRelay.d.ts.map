{"version": 3, "file": "WalletLinkRelay.d.ts", "sourceRoot": "", "sources": ["../../../../src/sign/walletlink/relay/WalletLinkRelay.ts"], "names": [], "mappings": "AAEA,OAAO,EAEL,kCAAkC,EACnC,MAAM,sCAAsC,CAAC;AAG9C,OAAO,EAAE,yBAAyB,EAAE,MAAM,qCAAqC,CAAC;AAEhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAChE,OAAO,EAAmB,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAMvE,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAC1D,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAIzE,MAAM,WAAW,sBAAsB;IACrC,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,kBAAkB,CAAC;IAC5B,QAAQ,EAAE,WAAW,CAAC;IACtB,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,IAAI,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;CAC9D;AAED,qBAAa,eAAgB,YAAW,kCAAkC;IACxE,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAqB;IAE7D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAS;IACpC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAqB;IAC7C,OAAO,CAAC,QAAQ,CAAoB;IACpC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAoB;IACtD,OAAO,CAAC,UAAU,CAAuB;IACzC,OAAO,CAAC,gBAAgB,CAA8B;IACtD,OAAO,CAAC,mBAAmB,CAAmC;IAC9D,OAAO,CAAC,aAAa,CAAgD;IAErE,OAAO,CAAC,EAAE,CAAU;IACpB,OAAO,CAAC,WAAW,CAAiB;IAEpC,OAAO,CAAC,QAAQ,CAAc;IAC9B,QAAQ,EAAE,OAAO,GAAG,SAAS,CAAC;IAC9B,oBAAoB,EAAE,OAAO,GAAG,SAAS,CAAC;gBAE9B,OAAO,EAAE,QAAQ,CAAC,sBAAsB,CAAC;IAoBrD,OAAO,CAAC,SAAS;IAiBjB,aAAa,WAAY,OAAO,UAkB9B;IAEF,eAAe,QAAS,MAAM,SAAS,MAAM,UAE3C;IAEF,YAAY,YAAa,MAAM,cAAc,MAAM,UAejD;IAEF,cAAc,oBAAqB,MAAM,UAgBvC;IAEK,cAAc,IAAI,IAAI;IAsBtB,uBAAuB,CAAC,MAAM,EAAE,yBAAyB;IAqBzD,gCAAgC,CAAC,MAAM,EAAE,yBAAyB;IAqBlE,yBAAyB,CAAC,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAUpE,oBAAoB;IAIpB,WAAW,CAChB,aAAa,SAAS,UAAU,EAChC,cAAc,SAAS,UAAU,GAAG,aAAa,EACjD,QAAQ,GAAG,YAAY,CAAC,cAAc,CAAC,EACvC,OAAO,EAAE,WAAW,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC;IAgCzD,OAAO,CAAC,uBAAuB;IAiB/B,OAAO,CAAC,0BAA0B;IA2BlC,OAAO,CAAC,+BAA+B;IAQvC,OAAO,CAAC,YAAY;IAQpB,yBAAyB,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY;IAU5D,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,cAAc;IAQf,uBAAuB;IA4B9B,UAAU,CACR,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,MAAM,EACf,MAAM,CAAC,EAAE,MAAM,EACf,QAAQ,CAAC,EAAE,MAAM,EACjB,KAAK,CAAC,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,MAAM,GACf,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IA8CtC,gBAAgB,CACd,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EAAE,EACjB,QAAQ,EAAE,MAAM,EAAE,EAClB,iBAAiB,EAAE,MAAM,EAAE,EAC3B,SAAS,CAAC,EAAE,MAAM,EAClB,cAAc,CAAC,EAAE;QACf,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;QACf,QAAQ,EAAE,MAAM,CAAC;KAClB;IA6CH,mBAAmB,CACjB,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,MAAM,GACf,OAAO,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;CA8ChD"}