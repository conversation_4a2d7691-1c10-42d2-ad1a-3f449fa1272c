{"version": 3, "file": "WalletLinkRelay.js", "sourceRoot": "", "sources": ["../../../../src/sign/walletlink/relay/WalletLinkRelay.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,EACL,oBAAoB,GAErB,MAAM,sCAAsC,CAAC;AAC9C,OAAO,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAG3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAEhE,OAAO,EAAE,eAAe,EAAgB,MAAM,wBAAwB,CAAC;AACvE,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAEtD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAEvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,OAAO,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAUjG,MAAM,OAAO,eAAe;IAmB1B,YAAY,OAAyC;QAV7C,wBAAmB,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,oCAAoC;QAI3F,gBAAW,GAAG,WAAW,EAAE,CAAC;QA2CpC,kBAAa,GAAG,CAAC,MAAe,EAAE,EAAE;YAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YAE1E,IAAI,MAAM,EAAE,CAAC;gBACX,2CAA2C;gBAC3C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAElC,IAAI,eAAe,EAAE,CAAC;gBACpB,MAAM,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAoB,CAAC;gBAChE,MAAM,yBAAyB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,MAAM,CAAC;gBACzF,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACzF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,oBAAe,GAAG,CAAC,GAAW,EAAE,KAAa,EAAE,EAAE;YAC/C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC;QAEF,iBAAY,GAAG,CAAC,OAAe,EAAE,UAAkB,EAAE,EAAE;YACrD,IACE,IAAI,CAAC,mBAAmB,CAAC,OAAO,KAAK,OAAO;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,UAAU,KAAK,UAAU,EAClD,CAAC;gBACD,OAAO;YACT,CAAC;YACD,IAAI,CAAC,mBAAmB,GAAG;gBACzB,OAAO;gBACP,UAAU;aACX,CAAC;YAEF,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC;QAEF,mBAAc,GAAG,CAAC,eAAuB,EAAE,EAAE;YAC3C,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,eAAe,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBACvD,kEAAkE;gBAClE,8DAA8D;gBAC9D,4CAA4C;gBAC5C,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;oBAC5E,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE;wBACtB,MAAM,EAAE,yBAAyB;wBACjC,MAAM,EAAE,CAAC,eAAgC,CAAC;qBAC3C,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,eAAe,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;YACpD,CAAC;QACH,CAAC,CAAC;QA7FA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE3C,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAErD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAEjD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;IACnB,CAAC;IAEO,SAAS;QACf,MAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE/F,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,MAAM,UAAU,GAAG,IAAI,oBAAoB,CAAC;YAC1C,OAAO;YACP,UAAU;YACV,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;QAE9E,UAAU,CAAC,OAAO,EAAE,CAAC;QAErB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;IACrC,CAAC;IA6DM,cAAc;QACnB,IAAI,CAAC,UAAU;aACZ,OAAO,EAAE;aACT,IAAI,CAAC,GAAG,EAAE;YACT;;;;;;;eAOG;YACH,MAAM,aAAa,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,EAAE,MAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAC3C,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YAChC,CAAC;YAED,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC7B,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,GAAE,CAAC,CAAC,CAAC;IACtB,CAAC;IAEM,uBAAuB,CAAC,MAAiC;QAC9D,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE;gBACN,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,QAAQ,EAAE,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACjD,IAAI,EAAE,mBAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC5C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI;gBACzF,YAAY,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI;gBACxF,oBAAoB,EAAE,MAAM,CAAC,aAAa;oBACxC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC;oBAC9C,CAAC,CAAC,IAAI;gBACR,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC1E,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,YAAY,EAAE,KAAK;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAEM,gCAAgC,CAAC,MAAiC;QACvE,OAAO,IAAI,CAAC,WAAW,CAAyD;YAC9E,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE;gBACN,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,QAAQ,EAAE,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACjD,IAAI,EAAE,mBAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC5C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI;gBACzF,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;gBACtF,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;oBAC/C,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,oBAAoB,CAAC;oBACrD,CAAC,CAAC,IAAI;gBACR,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC1E,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,YAAY,EAAE,IAAI;aACnB;SACF,CAAC,CAAC;IACL,CAAC;IAEM,yBAAyB,CAAC,iBAAyB,EAAE,OAAe;QACzE,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM,EAAE,2BAA2B;YACnC,MAAM,EAAE;gBACN,iBAAiB,EAAE,mBAAmB,CAAC,iBAAiB,EAAE,IAAI,CAAC;gBAC/D,OAAO;aACR;SACF,CAAC,CAAC;IACL,CAAC;IAEM,oBAAoB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,WAAW,CAIhB,OAAmC;QACnC,IAAI,gBAAgB,GAAwB,IAAI,CAAC;QACjD,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;QACvB,CAAC,CAAC;QAEF,OAAO,IAAI,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC/C,CAAC;gBACC,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;oBACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;oBAC/C,QAAQ,EAAE,MAAM;oBAChB,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,wDAAwD;iBACjG,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;gBACrB,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;gBAClD,CAAC;gBAED,OAAO,CAAC,QAAoB,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,EAAU,EAAE,OAAoB;QAC9D,MAAM,OAAO,GAAwB,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;QAC3E,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC;aAC5C,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,GAAE,CAAC,CAAC;aACf,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAAE,EAAE;gBACzC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,GAAG,CAAC,OAAO;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEL,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,0BAA0B;IAClB,0BAA0B,CAAC,MAAkB;QACnD,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,YAAY,eAAe,CAAC;YAAE,OAAO;QAElD,0DAA0D;QAC1D,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,yBAAyB,CAAC,CAAC,+CAA+C;YAC/E,KAAK,qBAAqB,EAAE,mDAAmD;gBAC7E,OAAO;YACT;gBACE,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,GAAG,EAAE;oBACH,MAAM,CAAC,gBAAgB,CACrB,OAAO,EACP,GAAG,EAAE;wBACH,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;oBACtC,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;gBACJ,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;gBACF,IAAI,CAAC,EAAE,CAAC,0BAA0B,EAAE,CAAC;gBACrC,MAAM;QACV,CAAC;IACH,CAAC;IAEO,+BAA+B,CAAC,EAAU;QAChD,MAAM,OAAO,GAAwB;YACnC,IAAI,EAAE,uBAAuB;YAC7B,EAAE;SACH,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAClE,CAAC;IAEO,YAAY,CAClB,KAAa,EACb,OAA4B,EAC5B,WAAoB;QAEpB,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACnE,CAAC;IAED,yBAAyB,CAAC,EAAU,EAAE,QAAsB;QAC1D,IAAI,QAAQ,CAAC,MAAM,KAAK,yBAAyB,EAAE,CAAC;YAClD,eAAe,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC7F,eAAe,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;YAClD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IAEO,mBAAmB,CAAC,EAAU,EAAE,MAAkB,EAAE,KAAa;;QACvE,MAAM,YAAY,GAAG,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,mCAAI,4BAA4B,CAAC;QACpE,IAAI,CAAC,yBAAyB,CAAC,EAAE,EAAE;YACjC,MAAM;YACN,YAAY;SACb,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,EAAU,EAAE,QAAsB;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEM,uBAAuB;QAC5B,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9C,MAAM,OAAO,GAAgB;YAC3B,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE;gBACN,OAAO;gBACP,UAAU;aACX;SACF,CAAC;QAEF,MAAM,gBAAgB,GAAwB,IAAI,CAAC;QACnD,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAE7B,OAAO,IAAI,OAAO,CAA0C,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9E,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACpD,6DAA6D;gBAC7D,aAAa;gBACb,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;gBACrB,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;gBAClD,CAAC;gBACD,OAAO,CAAC,QAAmD,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YACH,eAAe,CAAC,yBAAyB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClD,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU,CACR,IAAY,EACZ,OAAe,EACf,MAAe,EACf,QAAiB,EACjB,KAAc,EACd,OAAgB;QAEhB,MAAM,OAAO,GAAgB;YAC3B,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE;gBACN,IAAI;gBACJ,OAAO,EAAE;oBACP,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,KAAK;iBACN;gBACD,OAAO;aACR;SACF,CAAC;QAEF,IAAI,gBAAgB,GAAwB,IAAI,CAAC;QACjD,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;QACvB,CAAC,CAAC;QAEF,CAAC;YACC,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;gBACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,QAAQ,EAAE,MAAM;gBAChB,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,wDAAwD;aACjG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;gBAErB,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;gBAClD,CAAC;gBACD,OAAO,CAAC,QAAsC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CACd,OAAe,EACf,OAAiB,EACjB,QAAkB,EAClB,iBAA2B,EAC3B,SAAkB,EAClB,cAIC;QAED,MAAM,OAAO,GAAgB;YAC3B,MAAM,EAAE,kBAAkB;YAC1B,MAAM,EAAE;gBACN,OAAO;gBACP,OAAO;gBACP,iBAAiB;gBACjB,SAAS;gBACT,QAAQ;gBACR,cAAc;aACf;SACF,CAAC;QAEF,IAAI,gBAAgB,GAAwB,IAAI,CAAC;QACjD,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;QACvB,CAAC,CAAC;QAEF,CAAC;YACC,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;gBACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,QAAQ,EAAE,MAAM;gBAChB,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,wDAAwD;aACjG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,OAAO,CAAmC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;gBAErB,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;gBAClD,CAAC;gBACD,OAAO,CAAC,QAA4C,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB,CACjB,OAAe,EACf,OAAgB;QAEhB,MAAM,OAAO,GAAgB;YAC3B,MAAM,EAAE,qBAAqB;YAC7B,MAAM,kBACJ,OAAO,IACJ,EAAE,OAAO,EAAE,CACf;SACF,CAAC;QAEF,IAAI,gBAAgB,GAAwB,IAAI,CAAC;QACjD,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;QACvB,CAAC,CAAC;QAEF,CAAC;YACC,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;gBACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,QAAQ,EAAE,MAAM;gBAChB,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,wDAAwD;aACjG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,OAAO,CAAsC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1E,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACpD,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;gBACrB,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACpD,OAAO,MAAM,CACX,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC7B,IAAI,EAAE,QAAQ,CAAC,SAAS;wBACxB,OAAO,EAAE,2EAA2E;qBACrF,CAAC,CACH,CAAC;gBACJ,CAAC;qBAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;gBAClD,CAAC;gBAED,OAAO,CAAC,QAA+C,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;;AAlfc,yCAAyB,GAAG,IAAI,GAAG,EAAU,AAApB,CAAqB"}