{"version": 3, "file": "RedirectDialog.js", "sourceRoot": "", "sources": ["../../../../../../../src/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAqB,CAAC,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEtD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,GAAG,MAAM,yBAAyB,CAAC;AAQ1C,MAAM,OAAO,cAAc;IAIzB;QAFQ,SAAI,GAAmB,IAAI,CAAC;QAGlC,IAAI,CAAC,QAAQ,GAAG,UAAU,EAAE,CAAC;IAC/B,CAAC;IAEM,MAAM;QACX,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC;QAC1C,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,cAAc,EAAE,CAAC;IACnB,CAAC;IAEM,OAAO,CAAC,KAA0B;QACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAEO,MAAM,CAAC,KAAiC;QAC9C,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QACvB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,MAAM,CACJ,EAAC,qBAAqB,oBAChB,KAAK,IACT,SAAS,EAAE,GAAG,EAAE;gBACd,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,EACD,QAAQ,EAAE,IAAI,CAAC,QAAQ,IACvB,EACF,IAAI,CAAC,IAAI,CACV,CAAC;IACJ,CAAC;CACF;AAED,MAAM,qBAAqB,GAKvB,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,EAAE,EAAE;IAChE,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;IAE1C,OAAO,CACL,EAAC,iBAAiB,IAAC,QAAQ,EAAE,QAAQ;QACnC,WAAK,KAAK,EAAC,yBAAyB;YAClC,iBAAQ,GAAG,CAAS;YACpB,WAAK,KAAK,EAAC,kCAAkC,EAAC,OAAO,EAAE,SAAS,GAAI;YACpE,WAAK,KAAK,EAAE,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC;gBACpD,aAAI,KAAK,CAAK;gBACd,cAAQ,OAAO,EAAE,aAAa,IAAG,UAAU,CAAU,CACjD,CACF,CACY,CACrB,CAAC;AACJ,CAAC,CAAC"}