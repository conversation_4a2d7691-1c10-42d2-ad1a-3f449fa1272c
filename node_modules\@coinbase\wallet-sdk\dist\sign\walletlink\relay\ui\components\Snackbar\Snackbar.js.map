{"version": 3, "file": "Snackbar.js", "sourceRoot": "", "sources": ["../../../../../../../src/sign/walletlink/relay/ui/components/Snackbar/Snackbar.tsx"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAqB,CAAC,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAEnD,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,GAAG,MAAM,mBAAmB,CAAC;AAEpC,MAAM,MAAM,GAAG,w0CAAw0C,CAAC;AACx1C,MAAM,QAAQ,GAAG,wtBAAwtB,CAAC;AAmB1uB,MAAM,OAAO,QAAQ;IAOnB;QALiB,UAAK,GAAG,IAAI,GAAG,EAAiC,CAAC;QAE1D,gBAAW,GAAG,CAAC,CAAC;QAChB,SAAI,GAAmB,IAAI,CAAC;QAGlC,IAAI,CAAC,QAAQ,GAAG,UAAU,EAAE,CAAC;IAC/B,CAAC;IAEM,MAAM,CAAC,EAAW;QACvB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,uBAAuB,CAAC;QAC9C,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAEM,WAAW,CAAC,SAAgC;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,CAAC;IACJ,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAEO,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,MAAM,CACJ;YACE,EAAC,iBAAiB,IAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAC1D,EAAC,gBAAgB,oBAAK,SAAS,IAAE,GAAG,EAAE,GAAG,IAAI,CAC9C,CAAC,CACgB,CAChB,EACN,IAAI,CAAC,IAAI,CACV,CAAC;IACJ,CAAC;CACF;AAED,MAAM,CAAC,MAAM,iBAAiB,GAEzB,CAAC,KAAK,EAAE,EAAE,CAAC,CACd,WAAK,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAAC;IAC5C,iBAAQ,GAAG,CAAS;IACpB,WAAK,KAAK,EAAC,kBAAkB,IAAE,KAAK,CAAC,QAAQ,CAAO,CAChD,CACP,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA6C,CAAC,EACzE,UAAU,EACV,OAAO,EACP,SAAS,GACV,EAAE,EAAE;IACH,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC3C,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,KAAK,CAAC,CAAC;IAE9D,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,MAAM,GAAG;YACb,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;gBACrB,SAAS,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,EAAE,CAAC,CAAC;YACL,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;gBACrB,WAAW,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC,EAAE,KAAK,CAAC;SACV,CAAC;QAEF,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC;IAEF,OAAO,CACL,WACE,KAAK,EAAE,IAAI,CACT,2BAA2B,EAC3B,MAAM,IAAI,kCAAkC,EAC5C,QAAQ,IAAI,oCAAoC,CACjD;QAED,WAAK,KAAK,EAAC,kCAAkC,EAAC,OAAO,EAAE,cAAc;YACnE,WAAK,GAAG,EAAE,MAAM,EAAE,KAAK,EAAC,yCAAyC,GAAG;YAAC,GAAG;YACxE,WAAK,KAAK,EAAC,0CAA0C,IAAE,OAAO,CAAO;YACrE,WAAK,KAAK,EAAC,iBAAiB;gBACzB,CAAC,QAAQ,IAAI,CACZ,WACE,KAAK,EAAC,IAAI,EACV,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,MAAM,EACX,KAAK,EAAC,4BAA4B;oBAElC,cAAQ,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,GAAG,CAC5C,CACP;gBACD,WAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAC,YAAY,EAAC,KAAK,EAAC,QAAQ,GAAG,CACpD,CACF;QACL,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CACpC,WAAK,KAAK,EAAC,gCAAgC,IACxC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAC5B,WACE,KAAK,EAAE,IAAI,CACT,qCAAqC,EACrC,MAAM,CAAC,KAAK,IAAI,4CAA4C,CAC7D,EACD,OAAO,EAAE,MAAM,CAAC,OAAO,EACvB,GAAG,EAAE,CAAC;YAEN,WACE,KAAK,EAAE,MAAM,CAAC,QAAQ,EACtB,MAAM,EAAE,MAAM,CAAC,SAAS,EACxB,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,MAAM,EACX,KAAK,EAAC,4BAA4B;gBAElC,yBACa,MAAM,CAAC,eAAe,eACtB,MAAM,CAAC,eAAe,EACjC,CAAC,EAAE,MAAM,CAAC,IAAI,EACd,IAAI,EAAC,SAAS,GACd,CACE;YACN,YACE,KAAK,EAAE,IAAI,CACT,0CAA0C,EAC1C,MAAM,CAAC,KAAK,IAAI,iDAAiD,CAClE,IAEA,MAAM,CAAC,IAAI,CACP,CACH,CACP,CAAC,CACE,CACP,CACG,CACP,CAAC;AACJ,CAAC,CAAC"}