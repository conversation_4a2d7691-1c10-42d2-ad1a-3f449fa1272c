/**
 * Validates user supplied preferences. Throws if keys are not valid.
 * @param preference
 */
export function validatePreferences(preference) {
    if (!preference) {
        return;
    }
    if (!['all', 'smartWalletOnly', 'eoaOnly'].includes(preference.options)) {
        throw new Error(`Invalid options: ${preference.options}`);
    }
    if (preference.attribution) {
        if (preference.attribution.auto !== undefined &&
            preference.attribution.dataSuffix !== undefined) {
            throw new Error(`Attribution cannot contain both auto and dataSuffix properties`);
        }
    }
}
//# sourceMappingURL=validatePreferences.js.map