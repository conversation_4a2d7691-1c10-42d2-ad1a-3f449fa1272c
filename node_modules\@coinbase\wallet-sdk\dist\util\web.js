import { NAME, VERSION } from '../sdk-info.js';
import { getCrossOriginOpenerPolicy } from './checkCrossOriginOpenerPolicy.js';
import { standardErrors } from '../core/error/errors.js';
const POPUP_WIDTH = 420;
const POPUP_HEIGHT = 540;
// Window Management
export function openPopup(url) {
    const left = (window.innerWidth - POPUP_WIDTH) / 2 + window.screenX;
    const top = (window.innerHeight - POPUP_HEIGHT) / 2 + window.screenY;
    appendAppInfoQueryParams(url);
    const popupId = `wallet_${crypto.randomUUID()}`;
    const popup = window.open(url, popupId, `width=${POPUP_WIDTH}, height=${POPUP_HEIGHT}, left=${left}, top=${top}`);
    popup === null || popup === void 0 ? void 0 : popup.focus();
    if (!popup) {
        throw standardErrors.rpc.internal('Pop up window failed to open');
    }
    return popup;
}
export function closePopup(popup) {
    if (popup && !popup.closed) {
        popup.close();
    }
}
function appendAppInfoQueryParams(url) {
    const params = {
        sdkName: NAME,
        sdkVersion: VERSION,
        origin: window.location.origin,
        coop: getCrossOriginOpenerPolicy(),
    };
    for (const [key, value] of Object.entries(params)) {
        url.searchParams.append(key, value.toString());
    }
}
//# sourceMappingURL=web.js.map