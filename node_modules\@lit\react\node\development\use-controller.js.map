{"version": 3, "file": "use-controller.js", "sources": ["../../src/use-controller.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from '@lit/reactive-element/reactive-controller.js';\n\nexport type ControllerConstructor<C extends ReactiveController> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: Array<any>): C;\n};\n\nconst microtask = Promise.resolve();\n\n/**\n * An implementation of ReactiveControllerHost that is driven by React hooks\n * and `useController()`.\n */\nclass ReactControllerHost<C extends ReactiveController>\n  implements ReactiveControllerHost\n{\n  /* @internal */\n  _primaryController!: C;\n  private _controllers: Array<ReactiveController> = [];\n  private _updateCompletePromise: Promise<boolean>;\n  /* @internal */\n  _updatePending = true;\n  private _resolveUpdate!: (value: boolean | PromiseLike<boolean>) => void;\n\n  /* @internal */\n  _isConnected = false;\n\n  private _kickCount: number;\n  // A function to trigger an update of the React component\n  private _kick: (k: number) => void;\n\n  constructor(kickCount: number, kick: (k: number) => void) {\n    this._kickCount = kickCount;\n    this._kick = kick;\n    this._updateCompletePromise = new Promise((res, _rej) => {\n      this._resolveUpdate = res;\n    });\n  }\n\n  addController(controller: ReactiveController): void {\n    this._controllers.push(controller);\n  }\n\n  removeController(controller: ReactiveController): void {\n    // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n    // splice do nothing.\n    this._controllers?.splice(this._controllers.indexOf(controller) >>> 0, 1);\n  }\n\n  requestUpdate(): void {\n    if (!this._updatePending) {\n      this._updatePending = true;\n      // Trigger a React update by updating some state\n      microtask.then(() => this._kick(++this._kickCount));\n    }\n  }\n\n  get updateComplete(): Promise<boolean> {\n    return this._updateCompletePromise;\n  }\n\n  /* @internal */\n  _connected() {\n    this._isConnected = true;\n    this._controllers.forEach((c) => c.hostConnected?.());\n  }\n\n  /* @internal */\n  _disconnected() {\n    this._isConnected = false;\n    this._controllers.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /* @internal */\n  _update() {\n    this._controllers.forEach((c) => c.hostUpdate?.());\n  }\n\n  /* @internal */\n  _updated() {\n    this._updatePending = false;\n    const resolve = this._resolveUpdate;\n    // Create a new updateComplete Promise for the next update,\n    // before resolving the current one.\n    this._updateCompletePromise = new Promise((res, _rej) => {\n      this._resolveUpdate = res;\n    });\n    this._controllers.forEach((c) => c.hostUpdated?.());\n    resolve(this._updatePending);\n  }\n}\n\n/**\n * Creates and stores a stateful ReactiveController instance and provides it\n * with a ReactiveControllerHost that drives the controller lifecycle.\n *\n * Use this hook to convert a ReactiveController into a React hook.\n *\n * @param React the React module that provides the base hooks. Must provide\n * `useState` and `useLayoutEffect`.\n * @param createController A function that creates a controller instance. This\n * function is given a ReactControllerHost to pass to the controller. The\n * create function is only called once per component.\n */\nexport const useController = <C extends ReactiveController>(\n  React: typeof window.React,\n  createController: (host: ReactiveControllerHost) => C\n): C => {\n  const {useState, useLayoutEffect} = React;\n\n  // State to force updates of the React component\n  const [kickCount, kick] = useState(0);\n\n  // Create and store the controller instance. We use useState() instead of\n  // useMemo() because React does not guarantee that it will preserve values\n  // created with useMemo().\n  // TODO (justinfagnani): since this controller are mutable, this may cause\n  // issues such as \"shearing\" with React concurrent mode. The solution there\n  // will likely be to snapshot the controller state with something like\n  // `useMutableSource`:\n  // https://github.com/reactjs/rfcs/blob/master/text/0147-use-mutable-source.md\n  // We can address this when React's concurrent mode is closer to shipping.\n\n  let shouldDisconnect = false;\n  const [host] = useState(() => {\n    const host = new ReactControllerHost<C>(kickCount, kick);\n    const controller = createController(host);\n    host._primaryController = controller;\n    // Note, calls to `useState` are expected to produce no side effects and in\n    // StrictMode this is enforced by not running effects for the first render.\n    //\n    // This happens in StrictMode:\n    // 1. Throw away render: component function runs but does not call effects\n    // 2. Real render: component function runs and *does* call effects,\n    // 2.a. if first render, run effects and\n    // 2.a.1 mount,\n    // 2.a.2 unmount,\n    // 2.a.3 remount\n    // 2b. if not first render, just run effects\n    //\n    // To preserve update lifecycle ordering and run it before this hook\n    // returns, run connected here but schedule and async disconnect (handles\n    // lifecycle balance for `(1) Throw away render`).\n    // The disconnect is cancelled if the effects actually run (handles\n    // `(2.a.1) Real render, mount`).\n    host._connected();\n    shouldDisconnect = true;\n    microtask.then(() => {\n      if (shouldDisconnect) {\n        host._disconnected();\n      }\n    });\n    return host;\n  });\n\n  host._updatePending = true;\n\n  // This effect runs only on mount/unmount of the component (via the empty\n  // deps array). If the controller has just been created, it's scheduled\n  // a disconnect so that it behaves correctly in StrictMode (see above).\n  // The returned callback here disconnects the host when the component is\n  // unmounted (handles `(2.a.2) Real render, unmount` above).\n  // And finally, if the component is disconnected when the effect runs, we\n  // connect it (handles `(2.a.3) Real render, remount`).\n  useLayoutEffect(() => {\n    shouldDisconnect = false;\n    if (!host._isConnected) {\n      host._connected();\n    }\n    return () => host._disconnected();\n  }, []);\n\n  // We use useLayoutEffect because we need updated() called synchronously\n  // after rendering.\n  useLayoutEffect(() => host._updated());\n\n  // TODO (justinfagnani): don't call in SSR\n  host._update();\n\n  return host._primaryController;\n};\n"], "names": [], "mappings": "AAAA;;;;AAIG;AAYH,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AAEpC;;;AAGG;AACH,MAAM,mBAAmB,CAAA;IAkBvB,WAAY,CAAA,SAAiB,EAAE,IAAyB,EAAA;QAbhD,IAAY,CAAA,YAAA,GAA8B,EAAE,CAAC;;QAGrD,IAAc,CAAA,cAAA,GAAG,IAAI,CAAC;;QAItB,IAAY,CAAA,YAAA,GAAG,KAAK,CAAC;AAOnB,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,sBAAsB,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AACtD,YAAA,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;AAC5B,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,aAAa,CAAC,UAA8B,EAAA;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACpC;AAED,IAAA,gBAAgB,CAAC,UAA8B,EAAA;;;AAG7C,QAAA,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;KAC3E;IAED,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;AAE3B,YAAA,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SACrD;KACF;AAED,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACpC;;IAGD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC;KACvD;;IAGD,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC;KAC1D;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;KACpD;;IAGD,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;;;QAGpC,IAAI,CAAC,sBAAsB,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AACtD,YAAA,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;AAC5B,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC;AACpD,QAAA,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KAC9B;AACF,CAAA;AAED;;;;;;;;;;;AAWG;MACU,aAAa,GAAG,CAC3B,KAA0B,EAC1B,gBAAqD,KAChD;AACL,IAAA,MAAM,EAAC,QAAQ,EAAE,eAAe,EAAC,GAAG,KAAK,CAAC;;IAG1C,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;IAYtC,IAAI,gBAAgB,GAAG,KAAK,CAAC;AAC7B,IAAA,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAK;QAC3B,MAAM,IAAI,GAAG,IAAI,mBAAmB,CAAI,SAAS,EAAE,IAAI,CAAC,CAAC;AACzD,QAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;;;;;;;;;;;;;;;;;;QAkBrC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,SAAS,CAAC,IAAI,CAAC,MAAK;YAClB,IAAI,gBAAgB,EAAE;gBACpB,IAAI,CAAC,aAAa,EAAE,CAAC;aACtB;AACH,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;;;;;;;IAS3B,eAAe,CAAC,MAAK;QACnB,gBAAgB,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;AACD,QAAA,OAAO,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;KACnC,EAAE,EAAE,CAAC,CAAC;;;IAIP,eAAe,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;;IAGvC,IAAI,CAAC,OAAO,EAAE,CAAC;IAEf,OAAO,IAAI,CAAC,kBAAkB,CAAC;AACjC;;;;"}