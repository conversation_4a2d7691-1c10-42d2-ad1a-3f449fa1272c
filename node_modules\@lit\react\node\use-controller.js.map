{"version": 3, "file": "use-controller.js", "sources": ["../src/use-controller.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from '@lit/reactive-element/reactive-controller.js';\n\nexport type ControllerConstructor<C extends ReactiveController> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: Array<any>): C;\n};\n\nconst microtask = Promise.resolve();\n\n/**\n * An implementation of ReactiveControllerHost that is driven by React hooks\n * and `useController()`.\n */\nclass ReactControllerHost<C extends ReactiveController>\n  implements ReactiveControllerHost\n{\n  /* @internal */\n  _primaryController!: C;\n  private _controllers: Array<ReactiveController> = [];\n  private _updateCompletePromise: Promise<boolean>;\n  /* @internal */\n  _updatePending = true;\n  private _resolveUpdate!: (value: boolean | PromiseLike<boolean>) => void;\n\n  /* @internal */\n  _isConnected = false;\n\n  private _kickCount: number;\n  // A function to trigger an update of the React component\n  private _kick: (k: number) => void;\n\n  constructor(kickCount: number, kick: (k: number) => void) {\n    this._kickCount = kickCount;\n    this._kick = kick;\n    this._updateCompletePromise = new Promise((res, _rej) => {\n      this._resolveUpdate = res;\n    });\n  }\n\n  addController(controller: ReactiveController): void {\n    this._controllers.push(controller);\n  }\n\n  removeController(controller: ReactiveController): void {\n    // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n    // splice do nothing.\n    this._controllers?.splice(this._controllers.indexOf(controller) >>> 0, 1);\n  }\n\n  requestUpdate(): void {\n    if (!this._updatePending) {\n      this._updatePending = true;\n      // Trigger a React update by updating some state\n      microtask.then(() => this._kick(++this._kickCount));\n    }\n  }\n\n  get updateComplete(): Promise<boolean> {\n    return this._updateCompletePromise;\n  }\n\n  /* @internal */\n  _connected() {\n    this._isConnected = true;\n    this._controllers.forEach((c) => c.hostConnected?.());\n  }\n\n  /* @internal */\n  _disconnected() {\n    this._isConnected = false;\n    this._controllers.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /* @internal */\n  _update() {\n    this._controllers.forEach((c) => c.hostUpdate?.());\n  }\n\n  /* @internal */\n  _updated() {\n    this._updatePending = false;\n    const resolve = this._resolveUpdate;\n    // Create a new updateComplete Promise for the next update,\n    // before resolving the current one.\n    this._updateCompletePromise = new Promise((res, _rej) => {\n      this._resolveUpdate = res;\n    });\n    this._controllers.forEach((c) => c.hostUpdated?.());\n    resolve(this._updatePending);\n  }\n}\n\n/**\n * Creates and stores a stateful ReactiveController instance and provides it\n * with a ReactiveControllerHost that drives the controller lifecycle.\n *\n * Use this hook to convert a ReactiveController into a React hook.\n *\n * @param React the React module that provides the base hooks. Must provide\n * `useState` and `useLayoutEffect`.\n * @param createController A function that creates a controller instance. This\n * function is given a ReactControllerHost to pass to the controller. The\n * create function is only called once per component.\n */\nexport const useController = <C extends ReactiveController>(\n  React: typeof window.React,\n  createController: (host: ReactiveControllerHost) => C\n): C => {\n  const {useState, useLayoutEffect} = React;\n\n  // State to force updates of the React component\n  const [kickCount, kick] = useState(0);\n\n  // Create and store the controller instance. We use useState() instead of\n  // useMemo() because React does not guarantee that it will preserve values\n  // created with useMemo().\n  // TODO (justinfagnani): since this controller are mutable, this may cause\n  // issues such as \"shearing\" with React concurrent mode. The solution there\n  // will likely be to snapshot the controller state with something like\n  // `useMutableSource`:\n  // https://github.com/reactjs/rfcs/blob/master/text/0147-use-mutable-source.md\n  // We can address this when React's concurrent mode is closer to shipping.\n\n  let shouldDisconnect = false;\n  const [host] = useState(() => {\n    const host = new ReactControllerHost<C>(kickCount, kick);\n    const controller = createController(host);\n    host._primaryController = controller;\n    // Note, calls to `useState` are expected to produce no side effects and in\n    // StrictMode this is enforced by not running effects for the first render.\n    //\n    // This happens in StrictMode:\n    // 1. Throw away render: component function runs but does not call effects\n    // 2. Real render: component function runs and *does* call effects,\n    // 2.a. if first render, run effects and\n    // 2.a.1 mount,\n    // 2.a.2 unmount,\n    // 2.a.3 remount\n    // 2b. if not first render, just run effects\n    //\n    // To preserve update lifecycle ordering and run it before this hook\n    // returns, run connected here but schedule and async disconnect (handles\n    // lifecycle balance for `(1) Throw away render`).\n    // The disconnect is cancelled if the effects actually run (handles\n    // `(2.a.1) Real render, mount`).\n    host._connected();\n    shouldDisconnect = true;\n    microtask.then(() => {\n      if (shouldDisconnect) {\n        host._disconnected();\n      }\n    });\n    return host;\n  });\n\n  host._updatePending = true;\n\n  // This effect runs only on mount/unmount of the component (via the empty\n  // deps array). If the controller has just been created, it's scheduled\n  // a disconnect so that it behaves correctly in StrictMode (see above).\n  // The returned callback here disconnects the host when the component is\n  // unmounted (handles `(2.a.2) Real render, unmount` above).\n  // And finally, if the component is disconnected when the effect runs, we\n  // connect it (handles `(2.a.3) Real render, remount`).\n  useLayoutEffect(() => {\n    shouldDisconnect = false;\n    if (!host._isConnected) {\n      host._connected();\n    }\n    return () => host._disconnected();\n  }, []);\n\n  // We use useLayoutEffect because we need updated() called synchronously\n  // after rendering.\n  useLayoutEffect(() => host._updated());\n\n  // TODO (justinfagnani): don't call in SSR\n  host._update();\n\n  return host._primaryController;\n};\n"], "names": ["microtask", "Promise", "resolve", "ReactControllerHost", "constructor", "kickCount", "kick", "this", "_controllers", "_updatePending", "_isConnected", "_kickCount", "_kick", "_updateCompletePromise", "res", "_rej", "_resolveUpdate", "addController", "controller", "push", "removeController", "splice", "indexOf", "requestUpdate", "then", "updateComplete", "_connected", "for<PERSON>ach", "c", "hostConnected", "_disconnected", "hostDisconnected", "_update", "hostUpdate", "_updated", "hostUpdated", "useController", "React", "createController", "useState", "useLayoutEffect", "shouldDisconnect", "host", "_primaryController"], "mappings": ";;;;;AAgBA,MAAMA,EAAYC,QAAQC,UAM1B,MAAMC,EAkBJ,WAAAC,CAAYC,EAAmBC,GAbvBC,KAAYC,EAA8B,GAGlDD,KAAcE,GAAG,EAIjBF,KAAYG,GAAG,EAObH,KAAKI,EAAaN,EAClBE,KAAKK,EAAQN,EACbC,KAAKM,EAAyB,IAAIZ,SAAQ,CAACa,EAAKC,KAC9CR,KAAKS,EAAiBF,CAAG,GAE5B,CAED,aAAAG,CAAcC,GACZX,KAAKC,EAAaW,KAAKD,EACxB,CAED,gBAAAE,CAAiBF,GAGfX,KAAKC,GAAca,OAAOd,KAAKC,EAAac,QAAQJ,KAAgB,EAAG,EACxE,CAED,aAAAK,GACOhB,KAAKE,IACRF,KAAKE,GAAiB,EAEtBT,EAAUwB,MAAK,IAAMjB,KAAKK,IAAQL,KAAKI,KAE1C,CAED,kBAAIc,GACF,OAAOlB,KAAKM,CACb,CAGD,CAAAa,GACEnB,KAAKG,GAAe,EACpBH,KAAKC,EAAamB,SAASC,GAAMA,EAAEC,mBACpC,CAGD,CAAAC,GACEvB,KAAKG,GAAe,EACpBH,KAAKC,EAAamB,SAASC,GAAMA,EAAEG,sBACpC,CAGD,CAAAC,GACEzB,KAAKC,EAAamB,SAASC,GAAMA,EAAEK,gBACpC,CAGD,CAAAC,GACE3B,KAAKE,GAAiB,EACtB,MAAMP,EAAUK,KAAKS,EAGrBT,KAAKM,EAAyB,IAAIZ,SAAQ,CAACa,EAAKC,KAC9CR,KAAKS,EAAiBF,CAAG,IAE3BP,KAAKC,EAAamB,SAASC,GAAMA,EAAEO,kBACnCjC,EAAQK,KAAKE,EACd,QAeU2B,EAAgB,CAC3BC,EACAC,KAEA,MAAMC,SAACA,EAAQC,gBAAEA,GAAmBH,GAG7BhC,EAAWC,GAAQiC,EAAS,GAYnC,IAAIE,GAAmB,EACvB,MAAOC,GAAQH,GAAS,KACtB,MAAMG,EAAO,IAAIvC,EAAuBE,EAAWC,GAC7CY,EAAaoB,EAAiBI,GA0BpC,OAzBAA,EAAKC,EAAqBzB,EAkB1BwB,EAAKhB,IACLe,GAAmB,EACnBzC,EAAUwB,MAAK,KACTiB,GACFC,EAAKZ,GACN,IAEIY,CAAI,IA2Bb,OAxBAA,EAAKjC,GAAiB,EAStB+B,GAAgB,KACdC,GAAmB,EACdC,EAAKhC,GACRgC,EAAKhB,IAEA,IAAMgB,EAAKZ,MACjB,IAIHU,GAAgB,IAAME,EAAKR,MAG3BQ,EAAKV,IAEEU,EAAKC,CAAkB"}